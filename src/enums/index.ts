/**
 * @description 对象类型（数据库类型）
 * @return 1-mysql 2-oracle 3-tds 4-dm
 */
export const DATASOURCE_TYPE = [
  { label: 'MySQL数据库', value: '1', color: 'green' },
  { label: 'Oracle数据库', value: '2', color: 'orange' },
  { label: 'TDSQL数据库', value: '3', color: 'pink' },
  { label: 'DM数据库', value: '4', color: 'blue' },
  { label: 'OB(MySql兼容)数据库', value: '5', color: 'red' },
  { label: 'OB(Oracle兼容)数据库', value: '6', color: 'purple' },

  { label: 'Windows操作系统', value: '301', color: '#ff7a45' },
  { label: 'Linux操作系统', value: '302', color: 'navy' },
  { label: 'Oracle数据库(基线)', value: '303', color: 'maroon' },
  { label: 'DM(达梦)数据库(基线)', value: '304', color: '#d4b106' },
  { label: 'Apache中间件', value: '305', color: 'fuchsia' },
  { label: 'Tomcat中间件', value: '306', color: 'silver' },
  { label: 'NGINX中间件', value: '307', color: 'lime' },
  { label: 'Redis中间件', value: '308', color: 'olive' },
];

/**
 * @description 告警等级
 * @return 1-正常 2-警告 3-错误
 */
export const WARN_LEVEL = [
  { label: '普通', value: '1', status: 'success' },
  { label: '警告', value: '2', status: 'warning' },
  { label: '错误', value: '3', status: 'error' },
];
/**
 * @description 规则类型
 * @return 1-DDL规范 2-索引规范 3-命令规范 4-索引优化 5-全局配置 6-索引失效 7-使用建议 8-DML规范 9-其它
 */
export const RULE_TYPE = [
  { label: 'DDL规范', value: '1' },
  { label: '索引规范', value: '2' },
  { label: '命名规范', value: '3' },
  { label: '索引优化', value: '4' },
  { label: '全局配置', value: '5' },
  { label: '索引失效', value: '6' },
  { label: '使用建议', value: '7' },
  { label: 'DML规范', value: '8' },
  { label: '其它', value: '9' },
  { label: '自定义', value: '99' },
  { label: '操作系统安全', value: '301' },
  { label: '数据库安全', value: '302' },
  { label: '中间件安全', value: '303' },
];
/**
 * @description 适用场景
 * @return 1-SQL提交 2-自动任务 3-SQL提交和自动任务
 */
export const APPLICABLE_SCENARIOS = [
  { label: 'SQL提交', value: '1' },
  { label: '自动任务', value: '2' },
  { label: 'SQL提交和自动任务', value: '3' },
];

/**
 * @description 规则来源
 * @return 1-系统规则 2-自定义-shell-脚本 3-自定义-python-脚本 4-自定义-代码生成
 */
export const RULE_RESOURCE = [
  { label: '系统规则', value: '1' },
  { label: '自定义-shell-脚本', value: '2' },
  { label: '自定义-python-脚本', value: '3' },
  { label: '自定义-代码生成', value: '4' },
];
/**
 * @description 审核目的
 * @return 1-正确性 2-可维护性 3-性能问题 4-安全性
 */
export const AUDIT_PURPOSE_TYPE = [
  { label: '正确性', value: '1' },
  { label: '可维护性', value: '2' },
  { label: '性能问题', value: '3' },
  { label: '安全性', value: '4' },
  { label: '无', value: '99' },
];
/**
 * @description 规则目标 ruleTargets
 * @return 1-存在_字段 2-命名_字段 3-禁止_值 99-无
 */
export const RULE_TARGETS_TYPE = [
  { label: '存在_字段', value: '1' },
  { label: '命名_字段', value: '2' },
  { label: '禁止_值', value: '3' },
  { label: '无', value: '99' },
];

/**
 * @description 操作对象 opertionTarget
 * @return 1-字段 2-数据库 3-事件 4-函数 5-索引 6-存储过程 7-表 8-表空间 9-触发器 10-用户 11-视图 99-无
 */
export const OPERANDS = [
  { label: '字段', value: '1' },
  { label: '数据库', value: '2' },
  { label: '事件', value: '3' },
  { label: '函数', value: '4' },
  { label: '索引', value: '5' },
  { label: '存储过程', value: '6' },
  { label: '表', value: '7' },
  { label: '表空间', value: '8' },
  { label: '触发器', value: '9' },
  { label: '用户', value: '10' },
  { label: '视图', value: '11' },
  { label: '无', value: '99' },
];

/**
 * @description 审核状态
 * @return 1-通过 2-不通过
 */
export const AUDIT_STATUS = [
  { label: '通过', value: '1', status: 'success' },
  { label: '不通过', value: '2', status: 'error' },
  // { label: '解析SQL失败', value: '3', status: 'error' },
];

/**
 * @description 适用SQL类型
 * @return 1-Select语句 2-插入语句 3-更新语句 4-删除语句 34-更新和删除 5-创建表 6-授权 99-无
 */
export const APPLICABLE_SQL_TYPE = [
  { label: 'Select语句', value: '1' },
  { label: '插入语句', value: '2' },
  { label: '更新语句', value: '3' },
  { label: '删除语句', value: '4' },
  { label: '更新和删除', value: '34' },
  { label: '创建表', value: '5' },
  { label: '授权', value: '6' },
  { label: '无', value: '99' },
  { label: '查询、插入、更新', value: '123' },
  { label: '查询、更新、删除', value: '134' },
];

/**
 * @description 审核参数值类型
 * @return 1-数字(Integer) 2-字符串(String) 3-布尔值(Boolean)
 */
export const AUDIT_VALUE_TYPE = [
  { label: '数字(Integer)', value: '1' },
  { label: '字符串(String)', value: '2' },
  { label: '布尔值(Boolean)', value: '3' },
];

/**
 * @description 审核类型
 * @return 1-动态 2-静态 3-动态
 */
const ALL_RULE_AUDIT_MODE = [
  { label: '动态', value: 1 },
  { label: '静态', value: 2 },
  { label: '基线', value: 3 },
];
export const RULE_AUDIT_MODE = ALL_RULE_AUDIT_MODE.filter((item) =>
  __FEATURE_TOGGLE__.supportedAuditType.includes(item.value),
);

/**
 * @description 工单审核状态
 * @return 1-通过 2-不通过
 */
export const WORK_ORDER_APPROVAL_STATUS = [
  { label: '提交', value: 1, color: 'success' },
  { label: '通过', value: 2, color: 'error' },
  { label: '拒绝', value: 3, color: 'error' },
];

/**
 * @description 工单状态
 */
export const WORK_ORDER_STATUS = [
  { label: '等待审批', value: 1, color: 'default' },
  { label: '审批通过', value: 2, color: 'processing' },
  { label: '审批不通过', value: 3, color: 'error' },
  { label: '正常结束', value: 4, color: 'success' },
  { label: '执行失败', value: 5, color: 'warning' },
  { label: '人工中止流程', value: 6, color: 'magenta' },
];

/**
 * @description 结果来源
 */
export const RESULT_SOURCE = [
  { label: '人工审核|创建工单|SQL提交(手工触发)', value: 1 },
  { label: '自动任务触发', value: 2 },
  { label: '快速审核触发', value: 4 },
];
