import { QuestionCircleOutlined } from '@ant-design/icons';
import { SelectLang as UmiSelectLang } from '@umijs/max';

import { Tooltip } from 'antd';

export type SiderTheme = 'light' | 'dark';

export const SelectLang = () => {
  return (
    <UmiSelectLang
      style={{
        padding: 4,
      }}
    />
  );
};

export const Question = () => {
  return (
    <div
      style={{
        display: 'flex',
        height: 26,
      }}
      onClick={() => {
        window.open('https://pro.ant.design/docs/getting-started');
      }}
    >
      <QuestionCircleOutlined />
    </div>
  );
};

/**
 * 基线规则：错误信息渲染表格
 * @param _
 * @param record
 * @returns
 */
export const RenderErrorMessage = (_: any, record: API.AuditParameterResponse) => {
  if (record.auditMode !== 3) {
    // 只有 auditMode 为 3 时才展示为子表格
    return record.errorMessage || '-';
  }
  const text = record.errorMessage;
  if (!text) return null;

  const rows = text
    .split('$$$$$')
    .map((row) => row.trim())
    .filter((row) => {
      const [baseline] = row.split('->').map((part) => part.trim());
      return !!baseline;
    });

  if (rows.length === 0) return null;

  return (
    <table style={{ width: '100%', borderCollapse: 'collapse', tableLayout: 'fixed' }}>
      <thead>
        <tr>
          <th
            style={{
              width: '50%',
              border: '1px solid #ccc',
              padding: '4px',
              background: '#fafafa',
            }}
          >
            基线要求
          </th>
          <th
            style={{
              width: '50%',
              border: '1px solid #ccc',
              padding: '4px',
              background: '#fafafa',
            }}
          >
            错误描述
          </th>
        </tr>
      </thead>
      <tbody>
        {rows.map((row, idx) => {
          const parts = row.split('->');
          const baseline = parts[0]?.trim() || '';
          const detail = parts[1]?.trim() || '-';
          return (
            <tr key={idx}>
              <td
                style={{
                  border: '1px solid #ccc',
                  padding: '4px',
                  width: '50%',
                  maxWidth: '50%',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                <Tooltip title={baseline}>
                  <span>{baseline}</span>
                </Tooltip>
              </td>

              <td
                style={{
                  border: '1px solid #ccc',
                  padding: '4px',
                  width: '50%',
                  maxWidth: '50%',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                <Tooltip title={detail}>
                  <span>{detail}</span>
                </Tooltip>
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
};
