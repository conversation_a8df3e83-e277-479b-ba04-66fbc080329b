// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 增加任务 POST /api/v1/sqlaudit/tasks/addTask */
export async function addTask(body: API.TaskAddRequest, options?: { [key: string]: any }) {
  return request<API.ResultString>('/api/v1/sqlaudit/tasks/addTask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除任务 DELETE /api/v1/sqlaudit/tasks/deleteTask/${param0} */
export async function deleteTask(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteTaskParams,
  options?: { [key: string]: any },
) {
  const { taskName: param0, ...queryParams } = params;
  return request<API.ResultInteger>(`/api/v1/sqlaudit/tasks/deleteTask/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 任务详情 GET /api/v1/sqlaudit/tasks/detailTask/${param0} */
export async function detailTask(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.detailTaskParams,
  options?: { [key: string]: any },
) {
  const { taskName: param0, ...queryParams } = params;
  return request<API.ResultTaskResponse>(`/api/v1/sqlaudit/tasks/detailTask/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 立即执行（静态）任务 GET /api/v1/sqlaudit/tasks/immediatelyExecuteStaticTask/${param0} */
export async function immediatelyExecuteStaticTask(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.immediatelyExecuteStaticTaskParams,
  options?: { [key: string]: any },
) {
  const { taskName: param0, ...queryParams } = params;
  return request<API.ResultInteger>(
    `/api/v1/sqlaudit/tasks/immediatelyExecuteStaticTask/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 分页查询规则 GET /api/v1/sqlaudit/tasks/listTask */
export async function listTask(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listTaskParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageTaskResponse>('/api/v1/sqlaudit/tasks/listTask', {
    method: 'GET',
    params: {
      // pageNum has a default value: 1
      pageNum: '1',
      // pageSize has a default value: 10
      pageSize: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 “某个任务” 的所有执行结果 GET /api/v1/sqlaudit/tasks/listTaskResults */
export async function listTaskResults(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listTaskResultsParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageTaskResultResponse>('/api/v1/sqlaudit/tasks/listTaskResults', {
    method: 'GET',
    params: {
      // pageNum has a default value: 1
      pageNum: '1',
      // pageSize has a default value: 10
      pageSize: '10',
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 “某个执行结果的详细”  GET /api/v1/sqlaudit/tasks/listTaskResultsSql */
export async function listTaskResultsSql(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listTaskResultsSqlParams,
  options?: { [key: string]: any },
) {
  return request<API.ResultIPageTaskResultSqlResponse>(
    '/api/v1/sqlaudit/tasks/listTaskResultsSql',
    {
      method: 'GET',
      params: {
        // pageNum has a default value: 1
        pageNum: '1',
        // pageSize has a default value: 10
        pageSize: '10',
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 暂停任务 GET /api/v1/sqlaudit/tasks/pauseTask/${param0} */
export async function pauseTask(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pauseTaskParams,
  options?: { [key: string]: any },
) {
  const { taskName: param0, ...queryParams } = params;
  return request<API.ResultInteger>(`/api/v1/sqlaudit/tasks/pauseTask/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 恢复任务 GET /api/v1/sqlaudit/tasks/resumeTask/${param0} */
export async function resumeTask(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.resumeTaskParams,
  options?: { [key: string]: any },
) {
  const { taskName: param0, ...queryParams } = params;
  return request<API.ResultInteger>(`/api/v1/sqlaudit/tasks/resumeTask/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
