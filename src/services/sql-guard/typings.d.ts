declare namespace API {
  type ApprovalFlowItem = {
    /** 审批顺序 */
    approvalOrder: number;
    /** 角色Id */
    roleId: string;
  };

  type ApprovalFlowResponse = {
    /** 审批流id */
    id?: string;
    /** 资源组id */
    resourceGroupId?: number;
    /** 角色id */
    roleId?: string;
    /** 审批顺序，决定角色审批的先后顺序。 */
    approvalOrder?: number;
    createdAt?: string;
    updatedAt?: string;
    /** 角色名称 */
    roleName?: string;
  };

  type AuditParameterResponse = {
    id?: string;
    /** 键 */
    paramKey?: string;
    /** 值 */
    paramValue?: string;
    /** 描述 */
    description?: string;
    /** 参数值类型 */
    paramValueType?: number;
    /** 参数值显示名  */
    paramValueTypeName?: string;
    /** 数据库类型值 */
    dbType?: number;
    /** 数据库类型显示名 */
    dbTypeName?: string;
    createdAt?: string;
    updatedAt?: string;
    auditMode?: number;
    errorMessage?: string;
  };

  type AuditParameterUpdateRequest = {
    /** 参数键 */
    paramKey: string;
    /** 支持的数据库类型 (可选值: 1-MySQL, 2-Oracle, 3-TDSQL, 4-DM, 5-OB(MySql兼容), 6-OB(Oracle兼容), , 301-系统安全等...) */
    dbType: number;
    /** 参数值 */
    paramValue: string;
  };

  type AuditReport = {
    数据库类型?: number;
    数据源名称?: string;
    模式?: number;
    SQL语句?: string;
    种类?: string;
    执行时间?: string;
    审核时间?: string;
    违反规则id?: number;
    违反规则名称?: string;
    审核状态?: number;
    错误信息?: string;
    结果来源?: number;
  };

  type AuditReportRequest = {
    开始时间?: string;
    结束时间?: string;
    数据库类型?: number;
    模式?: number;
    种类?: string;
    审核状态?: number;
    结果来源?: number;
    数据源id?: number;
    pageNum?: number;
    pageSize?: number;
  };

  type auditResultStatisticsDetailParams = {
    dbType?: number;
    auditMode?: number;
    auditStatus?: number;
    resultSource?: number;
    sqlKind?: string;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
    auditResultId: string;
    pageNum?: number;
    pageSize?: number;
  };

  type auditResultStatisticsExportParams = {
    dbType?: number;
    auditMode?: number;
    auditStatus?: number;
    resultSource?: number;
    sqlKind?: string;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
  };

  type AuditResultViolationsResponse = {
    /** 规则ID */
    id?: string;
    /** 规则名称 */
    ruleName?: string;
    /** 规则说明 */
    description?: string;
    /** 数据库类型 */
    dbType?: number;
    /** 数据库类型名称 */
    dbTypeName?: string;
    /** 告警等级 */
    alertLevel?: number;
    /** 告警等级名称 */
    alertLevelName?: string;
    /** 规则类型 */
    ruleType?: number;
    /** 规则类型名称 */
    ruleTypeName?: string;
    /** 适用场景 */
    applicableScene?: number;
    /** 适用场景名称 */
    applicableSceneName?: string;
    /** 适用SQL类型 */
    applicableSqlType?: number;
    /** 适用SQL类型名称 */
    applicableSqlTypeName?: string;
    /** 规则目标 */
    ruleTargets?: number;
    /** 规则目标名称 */
    ruleTargetsName?: string;
    /** 规则脚本 */
    ruleScript?: string;
    /** 规则来源 */
    ruleResource?: number;
    /** 规则来源名称 */
    ruleResourceName?: string;
    /** 阈值key */
    thresholdParamKey?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 违反规则的详细说明 */
    errorMessage?: string;
    /** 违反规则时规则的名称 */
    violatedRuleName?: string;
    /** 审核目的 */
    auditPurposeName?: number;
    /** 审核目的名称 */
    auditPurpose?: string;
  };

  type AuditRusultStatisticsDetailResponse = {
    /** 规则ID */
    ruleId?: string;
    /** 规则名称 */
    violatedRuleName?: string;
    /** 审核模式 */
    auditMode?: number;
    /** 错误描述 */
    errorMessage?: string;
  };

  type AuditRusultStatisticsResponse = {
    /** 数据库类型 */
    dbType?: number;
    /** 结果来源 */
    resultSource?: number;
    /** 审核模式 */
    auditMode?: number;
    /** 审核状态（审核结果） */
    auditStatus?: number;
    /** 审核信息（审核描述） */
    auditInfo?: string;
    /** sql语句 */
    sqlStatement?: string;
    /** 资源组名称 */
    groupName?: string;
    /** 数据源名称 */
    sourceName?: string;
    /** SQL种类（动态才有值） */
    sqlKind?: string;
    /** 审核时间 */
    createdAt?: string;
    /** 优化建议 */
    optimizedSql?: string;
    /** 违反规则数量 */
    violationsRuleCount?: number;
    /** 审核结果ID */
    auditResultId?: string;
  };

  type BatchIdRequest = {
    /** ids */
    ids?: string[];
  };

  type DashboardAuditCountBaseLine = {
    dbType?: number;
    auditPassedCountSql?: number;
    totalAuditCountSql?: number;
    auditFailedCountSql?: number;
  };

  type DashboardAuditCountSql = {
    dbType?: number;
    auditPassedCountSql?: number;
    totalAuditCountSql?: number;
    auditFailedCountSql?: number;
  };

  type DashboardAuditCountStatic = {
    dbType?: number;
    auditPassedCountSql?: number;
    totalAuditCountSql?: number;
    auditFailedCountSql?: number;
  };

  type DashboardAuditTime = {
    auditMode?: number;
    auditTime?: number;
  };

  type DashboardCatchSql = {
    taskName?: string;
    sqlCount?: number;
  };

  type DashboardDBCount = {
    dataSourceType?: number;
    dataSourceCount?: number;
  };

  type DashboardResponse = {
    /** 规则数量 */
    dashboardRuleCountList?: DashboardRuleCount[];
    /** 违反排名前10规则 */
    auditViolatedRuleList?: DashboardViolatedRule[];
    /** SQL审核状态 */
    auditCountSqlList?: DashboardAuditCountSql[];
    /** 静态审核状态 */
    auditCountStaticList?: DashboardAuditCountStatic[];
    /** 基线审核状态 */
    auditCountBaseLineList?: DashboardAuditCountBaseLine[];
    /** 整体审核次数 */
    auditTimeList?: DashboardAuditTime[];
    /** 任务运行情况 */
    auditTaskCountList?: DashboardTaskCount[];
    /** 采集SQL排名前10的任务 */
    auditCatchSqlList?: DashboardCatchSql[];
    /** 数据源数量 */
    dashboardDBCountList?: DashboardDBCount[];
  };

  type DashboardRuleCount = {
    auditMode?: number;
    auditModeName?: string;
    alertLevel?: number;
    alertLevelName?: string;
    count?: number;
  };

  type DashboardTaskCount = {
    taskIngCount?: number;
    taskEndCount?: number;
    taskFailCount?: number;
    taskTotalCount?: number;
  };

  type DashboardViolatedRule = {
    ruleName?: string;
    violationCount?: number;
    dbType?: number;
  };

  type DataSourceAddRequest = {
    /** 数据源名称 */
    sourceName: string;
    /** 数据库IP地址 */
    sourceIp: string;
    /** 数据库端口 */
    sourcePort?: number;
    /** 支持的数据库类型 (可选值: 1-MySQL, 2-Oracle, 3-TDSQL, 4-DM, 5-OB(MySql兼容), 6-OB(Oracle兼容), , 301-系统安全等...) */
    dbType: number;
    /** 数据库用户名 */
    username?: string;
    /** 数据库密码 */
    password?: string;
    /** 审核类型 */
    auditMode: number;
    /** 所属资源组ID */
    resourceGroupId: number;
    /** 规则模板ID */
    ruleTemplateId: string;
    /** 服务名称 */
    serviceName?: string;
    /** SID */
    sid?: string;
    /** 租户 */
    tenant?: string;
    /** 集群 */
    cluster?: string;
    /** 主机端口 */
    hostPort?: number;
    /** 主机账号 */
    hostAccount?: string;
    /** 主机密码 */
    hostPassword?: string;
  };

  type DataSourceGroupInfoResponse = {
    /** 数据库类型 */
    dbType?: number;
    /** 数据库类型名称 */
    dbTypeName?: string;
    /** 聚合查询结果 */
    sourceInfo?: string;
  };

  type DataSourceResponse = {
    /** 数据源ID */
    sourceId?: number;
    /** 数据源名称 */
    sourceName?: string;
    /** 数据库IP地址 */
    sourceIp?: string;
    /** 数据库端口 */
    sourcePort?: number;
    /** 数据库用户名 */
    username?: string;
    /** 数据库密码 */
    password?: string;
    /** 所属资源组ID */
    resourceGroupId?: number;
    /** 所屬資源組名稱 */
    resourceGroupName?: string;
    /** 规则模板ID */
    ruleTemplateId?: string;
    /** 规则模板名称 */
    ruleTemplateName?: string;
    /** 服务名称 */
    serviceName?: string;
    /** SID */
    sid?: string;
    /** 数据库类型 */
    dbType?: number;
    /** 数据库类型名称 */
    dbTypeName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 租户 */
    tenant?: string;
    /** 集群 */
    cluster?: string;
    /** 主机端口 */
    hostPort?: number;
    /** 主机账号 */
    hostAccount?: string;
    /** 主机密码 */
    hostPassword?: string;
    /** 审核类型 */
    auditMode?: number;
    /** 审核类型名称 */
    auditModeName?: string;
  };

  type DataSourceTestRequest = {
    /** 数据库IP地址 */
    sourceIp: string;
    /** 数据库端口 */
    sourcePort?: number;
    /** 支持的数据库类型 (可选值: 1-MySQL, 2-Oracle, 3-TDSQL, 4-DM, 5-OB(MySql兼容), 6-OB(Oracle兼容), , 301-系统安全等...) */
    dbType: number;
    /** 数据库用户名 */
    username?: string;
    /** 数据库密码 */
    password?: string;
    /** 服务名称 */
    serviceName?: string;
    /** SID */
    sid?: string;
    /** 租户 */
    tenant?: string;
    /** 集群 */
    cluster?: string;
    /** 主机端口 */
    hostPort?: number;
    /** 主机账号 */
    hostAccount?: string;
    /** 主机密码 */
    hostPassword?: string;
  };

  type DataSourceUpdateRequest = {
    /** 数据源ID */
    sourceId: number;
    /** 数据源名称 */
    sourceName: string;
    /** 数据库IP地址 */
    sourceIp: string;
    /** 数据库端口 */
    sourcePort?: number;
    /** 数据库用户名 */
    username?: string;
    /** 数据库密码 */
    password?: string;
    /** 所属资源组ID */
    resourceGroupId: number;
    /** 规则模板ID */
    ruleTemplateId: string;
    /** 服务名称 */
    serviceName?: string;
    /** SID */
    sid?: string;
    /** 支持的数据库类型 (可选值: 1-MySQL, 2-Oracle, 3-TDSQL, 4-DM, 5-OB(MySql兼容), 6-OB(Oracle兼容), , 301-系统安全等...) */
    dbType: number;
    /** 租户 */
    tenant?: string;
    /** 集群 */
    cluster?: string;
    /** 主机端口 */
    hostPort?: number;
    /** 主机账号 */
    hostAccount?: string;
    /** 主机密码 */
    hostPassword?: string;
  };

  type deleteDataSourceParams = {
    id: number;
  };

  type deleteResourceGroupParams = {
    id: number;
  };

  type deleteRuleParams = {
    id: string;
  };

  type deleteRuleTemplateParams = {
    id: string;
  };

  type deleteSubsysRoleParams = {
    id: string;
  };

  type deleteTaskParams = {
    taskName: string;
  };

  type detailDataSourceParams = {
    id: string;
  };

  type detailResourceGroupParams = {
    groupId: number;
  };

  type detailRuleParams = {
    id: string;
  };

  type detailRuleTemplateParams = {
    id: string;
  };

  type detailStatisticsParams = {
    dbType?: number;
    auditMode?: number;
    auditStatus?: number;
    resultSource?: number;
    sqlKind?: string;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
    pageNum?: number;
    pageSize?: number;
  };

  type detailSubsysRoleParams = {
    id: string;
  };

  type detailTaskParams = {
    taskName: string;
  };

  type downloadReport1Params = {
    dbType?: number;
    auditMode?: number;
    auditStatus?: number;
    resultSource?: number;
    sqlKind?: string;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
  };

  type getApprovalFlowByResourceGroupIdParams = {
    resourceGroupId: number;
  };

  type getApprovalHistoryByWorkOrderIdParams = {
    workOrderId: number;
  };

  type getBaseLineDetailsListByRuleIdAndTemplateIdParams = {
    templateId: number;
    ruleId: string;
  };

  type getBaseLineDetailsListByRuleIdParams = {
    ruleId: string;
  };

  type getParameterParams = {
    paramKey?: string;
    dbType?: number;
  };

  type getPermissionByRoleIdParams = {
    roleId: string;
  };

  type getWorkOrderDetailParams = {
    workOrderId: number;
  };

  type immediatelyExecuteStaticTaskParams = {
    taskName: string;
  };

  type IPageAuditParameterResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: AuditParameterResponse[];
    total?: string;
  };

  type IPageAuditReport = {
    size?: string;
    pages?: string;
    current?: string;
    records?: AuditReport[];
    total?: string;
  };

  type IPageAuditResultViolationsResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: AuditResultViolationsResponse[];
    total?: string;
  };

  type IPageAuditRusultStatisticsDetailResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: AuditRusultStatisticsDetailResponse[];
    total?: string;
  };

  type IPageAuditRusultStatisticsResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: AuditRusultStatisticsResponse[];
    total?: string;
  };

  type IPageDataSourceResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: DataSourceResponse[];
    total?: string;
  };

  type IPageOriginalSqlDetailResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: OriginalSqlDetailResponse[];
    total?: string;
  };

  type IPageOriginalSqlResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: OriginalSqlResponse[];
    total?: string;
  };

  type IPageResourceGroupIdAndNameResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: ResourceGroupIdAndNameResponse[];
    total?: string;
  };

  type IPageResourceGroupResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: ResourceGroupResponse[];
    total?: string;
  };

  type IPageRoleResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: RoleResponse[];
    total?: string;
  };

  type IPageRuleResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: RuleResponse[];
    total?: string;
  };

  type IPageRuleTemplateResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: RuleTemplateResponse[];
    total?: string;
  };

  type IPageSubsysPermissionResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: SubsysPermissionResponse[];
    total?: string;
  };

  type IPageTaskResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: TaskResponse[];
    total?: string;
  };

  type IPageTaskResultResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: TaskResultResponse[];
    total?: string;
  };

  type IPageTaskResultSqlResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: TaskResultSqlResponse[];
    total?: string;
  };

  type IPageViolationsRuleStatisticsDetailResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: ViolationsRuleStatisticsDetailResponse[];
    total?: string;
  };

  type IPageViolationsRuleStatisticsResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: ViolationsRuleStatisticsResponse[];
    total?: string;
  };

  type IPageWorkOrderResponse = {
    size?: string;
    pages?: string;
    current?: string;
    records?: WorkOrderResponse[];
    total?: string;
  };

  type listDataSourceParams = {
    sourceName?: string;
    sourceIp?: string;
    dbType: number;
    auditMode: number;
    pageNum?: number;
    pageSize?: number;
  };

  type listDbNameOrSchemaBysourceIdParams = {
    sourceId: number;
  };

  type listDSByGroupIdAndAuditModeParams = {
    groupId: number;
    auditMode: number;
  };

  type listParameterParams = {
    paramKey?: string;
    dbType?: number;
    pageNum?: number;
    pageSize?: number;
  };

  type listPermissionParams = {
    description?: string;
    pageNum?: number;
    pageSize?: number;
  };

  type listResourceGroupParams = {
    groupName?: string;
    pageNum?: number;
    pageSize?: number;
  };

  type listRoleParams = {
    name?: string;
    pageNum?: number;
    pageSize?: number;
  };

  type listRuleByDbTypeParams = {
    dbType: number;
    auditMode: number;
    ruleResource?: number;
    ruleName?: string;
    ruleTargets?: number;
    ruleType?: number;
    pageNum?: number;
    pageSize?: number;
  };

  type listRuleByTemplateIdParams = {
    templateId: number;
    ruleResource?: number;
    ruleName?: string;
    ruleTargets?: number;
    ruleType?: number;
    pageNum?: number;
    pageSize?: number;
  };

  type listRulesParams = {
    ruleName?: string;
    dbType: number;
    auditMode: number;
    pageNum?: number;
    pageSize?: number;
  };

  type listRuleTemplateParams = {
    dbType: number;
    templateName?: string;
    auditMode?: number;
    pageNum?: number;
    pageSize?: number;
  };

  type listSqlDetailParams = {
    sqlSha256: string;
    pageNo?: number;
    pageSize?: number;
  };

  type listSqlRecordsParams = {
    sourceId?: number;
    sqlText?: string;
    pageNo?: number;
    pageSize?: number;
  };

  type listTaskParams = {
    pageNum?: number;
    pageSize?: number;
  };

  type listTaskResultsParams = {
    taskName: string;
    machineIp: string;
    pageNum?: number;
    pageSize?: number;
  };

  type listTaskResultsSqlParams = {
    taskResultId: string;
    pageNum?: number;
    pageSize?: number;
  };

  type listUnusedRuleByTemplateIdParams = {
    templateId: number;
    dbType: number;
    auditMode: number;
    ruleResource?: number;
    ruleName?: string;
    ruleTargets?: number;
    ruleType?: number;
    pageNum?: number;
    pageSize?: number;
  };

  type listUserOfGroupParams = {
    groupId: number;
  };

  type listUserOfRoleParams = {
    roleId: string;
  };

  type listViolationsByAuditResultIdParams = {
    auditResultId: string;
    pageNum?: number;
    pageSize?: number;
  };

  type listWorkOrderPageParams = {
    pageNum?: number;
    pageSize?: number;
  };

  type LoginRequest = {
    account?: string;
    password?: string;
  };

  type LoginVO = {
    user?: UserVO;
    token?: string;
  };

  type MachineInfo = {
    ip?: string;
    account?: string;
    password?: string;
    logDir?: string;
  };

  type MachineInfoDTO = {
    /** IP地址 */
    ip: string;
    /** 账户名 */
    account: string;
    /** 密码 */
    password: string;
    /** 日志目录 */
    logDir?: string;
  };

  type NotificationPageRequest = {
    page?: PageRequest;
  };

  type OriginalSqlDetailResponse = {
    /** sql语句 */
    sqlStatement?: string;
    /** 优化建议 */
    optimizedSql?: string;
    /** 捕获时间 */
    updatedAt?: string;
  };

  type OriginalSqlResponse = {
    /** shar256 */
    sqlSha256?: string;
    /** 原始sql */
    sqlText?: string;
    /** 数据源Id */
    sourceId?: number;
    /** 数据源名称 */
    sourceName?: string;
    /** 使用次数 */
    usageCount?: number;
    /** 创建时间 */
    createdAt?: string;
  };

  type PageRequest = {
    /** 数量 */
    size?: number;
    /** 页码 */
    page?: number;
  };

  type PageVOSystemNotificationVO = {
    /** 数据 */
    data?: SystemNotificationVO[];
    /** 页码 */
    page?: number;
    /** 数量 */
    size?: number;
    /** 总数 */
    total?: string;
    /** 总页 */
    pageTotal?: number;
  };

  type pauseTaskParams = {
    taskName: string;
  };

  type previewReportParams = {
    dbType?: number;
    auditMode?: number;
    auditStatus?: number;
    resultSource?: number;
    sqlKind?: string;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
  };

  type QuickAuditRequest = {
    auditMode: number;
    dbType: number;
    ruleTemplateId: number;
    targetObjectIp: string;
    targetObjectPort: string;
    targetObjectAccount: string;
    targetObjectPassword: string;
    serviceName?: string;
    sid?: string;
    tenant?: string;
    cluster?: string;
    dbNameOrSchema?: string;
    /** SQL语句列表 */
    sql?: string[];
  };

  type RedirectInfoVO = {
    url?: string;
    businessId?: string;
    redirectType?: string;
  };

  type ResourceGroupAddRequest = {
    /** 资源组名称 */
    groupName: string;
    /** 资源组描述 */
    groupDescription: string;
  };

  type ResourceGroupApprovalFlowRequest = {
    /** 资源组Id */
    resourceGroupId: number;
    /** 审批流程列表，包含角色Id和审批顺序 */
    approvalFlowList: ApprovalFlowItem[];
  };

  type ResourceGroupIdAndNameResponse = {
    /** 资源组ID */
    groupId?: string;
    /** 资源组名称 */
    groupName?: string;
  };

  type ResourceGroupResponse = {
    /** 资源组ID */
    groupId?: string;
    /** 资源组名称 */
    groupName?: string;
    /** 资源组描述 */
    groupDescription?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type ResourceGroupUpdateRequest = {
    /** 资源组ID */
    groupId: string;
    /** 资源组名称 */
    groupName: string;
    /** 资源组描述 */
    groupDescription: string;
  };

  type ResourceGroupUsersRequest = {
    /** 资源组Id */
    resourceGroupId: string;
    /** 用户Id */
    userIds?: string[];
  };

  type ResultAuditParameterResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: AuditParameterResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultDashboardResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: DashboardResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultDataSourceResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: DataSourceResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultInteger = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: number;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageAuditParameterResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageAuditParameterResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageAuditReport = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageAuditReport;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageAuditResultViolationsResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageAuditResultViolationsResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageAuditRusultStatisticsDetailResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageAuditRusultStatisticsDetailResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageAuditRusultStatisticsResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageAuditRusultStatisticsResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageDataSourceResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageDataSourceResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageOriginalSqlDetailResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageOriginalSqlDetailResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageOriginalSqlResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageOriginalSqlResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageResourceGroupIdAndNameResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageResourceGroupIdAndNameResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageResourceGroupResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageResourceGroupResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageRoleResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageRoleResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageRuleResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageRuleResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageRuleTemplateResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageRuleTemplateResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageSubsysPermissionResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageSubsysPermissionResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageTaskResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageTaskResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageTaskResultResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageTaskResultResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageTaskResultSqlResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageTaskResultSqlResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageViolationsRuleStatisticsDetailResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageViolationsRuleStatisticsDetailResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageViolationsRuleStatisticsResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageViolationsRuleStatisticsResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultIPageWorkOrderResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: IPageWorkOrderResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListApprovalFlowResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ApprovalFlowResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListDataSourceGroupInfoResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: DataSourceGroupInfoResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListResourceGroupIdAndNameResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: ResourceGroupIdAndNameResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListRuleBaseLineDetailResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: RuleBaseLineDetailResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListSqlAuditResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: SqlAuditResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListSqlAuditUserResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: SqlAuditUserResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListString = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: string[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListSubsysPermissionResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: SubsysPermissionResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListSummaryStatisticsResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: SummaryStatisticsResponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListSystemNotificationVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: SystemNotificationVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListUserVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: UserVO[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultListWorkOrderApprovalRecordReponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: WorkOrderApprovalRecordReponse[];
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultLoginVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: LoginVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultLong = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: string;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultObject = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: Record<string, any>;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultPageVOSystemNotificationVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: PageVOSystemNotificationVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultResourceGroupResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: ResourceGroupResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultRoleResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: RoleResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultRuleResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: RuleResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultRuleTemplateResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: RuleTemplateResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultSqlFormatDTO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: SqlFormatDTO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultString = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    /** 数据 */
    data?: string;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultTaskResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: TaskResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultUserVO = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: UserVO;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type ResultWorkOrderDetailResponse = {
    /** 结果状态码 */
    code?: number;
    /** 响应信息 */
    message?: string;
    data?: WorkOrderDetailResponse;
    /** 接口请求时间 */
    timestamp?: string;
  };

  type resumeTaskParams = {
    taskName: string;
  };

  type RoleAddRequest = {
    /** 角色名称 */
    name: string;
    /** 角色描述 */
    description: string;
  };

  type RoleResponse = {
    /** 角色id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 角色描述 */
    description?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
  };

  type RolesPermissionRequest = {
    /** 角色Id */
    roleId: string;
    /** 权限ID */
    permissionIds?: string[];
  };

  type RolesUsersRequest = {
    /** 角色Id */
    roleId: string;
    /** 用户Id */
    userIds?: string[];
  };

  type RoleUpdateRequest = {
    /** 角色ID */
    id: string;
    /** 角色名称 */
    name: string;
    /** 角色描述 */
    description: string;
  };

  type RuleAddRequest = {
    /** 规则名称 */
    ruleName: string;
    /** 规则说明 */
    description: string;
    /** 规则来源 (可选值: 1-系统规则（不可选）, 2-自定义-shell-脚本, 3-自定义-python-脚本, 4-自定义-代码生成) */
    ruleResource: number;
    /** 支持的数据库类型 (可选值: 1-MySQL, 2-Oracle, 3-TDSQL, 4-DM, 5-OB(MySql兼容), 6-OB(Oracle兼容), , 301-系统安全等...) */
    dbType: number;
    /** 告警等级 (可选值: 1-正常, 2-警告, 3-错误) */
    alertLevel: number;
    /** 规则类型 (可选值: 1-DDL规范, 2-索引规范, 3-命令规范, 4-索引优化, 5-全局配置, 6-索引失效, 7-使用建议, 8-DML规范, 9-其它, 301-操作系统安全, 302-数据库安全, 303-中间件安全) */
    ruleType: number;
    /** 适用场景 (可选值: 1-SQL上线|创建工单, 2-自动任务, 3-SQL上线和自动任务) */
    applicableScene: number;
    /** 适用SQL类型 (可选值: 1-Select语句, 2-插入语句, 3-更新语句, 4-删除语句, 34-更新和删除, 123-查询、插入、更新, 5-数据定义语言（DDL）) */
    applicableSqlType: number;
    /** 规则目标 (可选值: 1-存在_字段, 2-命名_字段, 3-禁止_值, 99-无) */
    ruleTargets: number;
    /** 规则脚本 */
    ruleScript: string;
    /** 审核目的类型 (可选值: 1-正确性, 2-可维护性, 3-性能问题, 4-安全性) */
    auditPurpose: number;
    /** 审核模式 (可选值: 1-动态, 2-静态, 3-基线) */
    auditMode: number;
  };

  type RuleBaseLineDetailResponse = {
    /** 主键ID */
    id?: string;
    /** 规则ID */
    ruleId?: string;
    /** 基线技术要求 */
    baseLineRequirement?: string;
    /** 基线标准点（参数） */
    baseLineStandardPoint?: string;
    /** 说明 */
    description?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 启用状态 */
    enabledStatus?: number;
  };

  type RuleBaseLineDetailUpdateRequest = {
    /**  模板ID */
    templateId: string;
    /** 规则ID */
    ruleId: string;
    /** 基线Id */
    baseLineId: string;
    /** 基线标准点（参数） */
    baseLineStandardPoint?: string;
    /** 1-启用, 2-禁用 */
    enabledStatus?: number;
  };

  type RuleResponse = {
    /** 规则ID */
    id?: string;
    /** 规则名称 */
    ruleName?: string;
    /** 规则说明 */
    description?: string;
    /** 数据库类型 */
    dbType?: number;
    /** 数据库类型名称 */
    dbTypeName?: string;
    /** 告警等级 */
    alertLevel?: number;
    /** 告警等级名称 */
    alertLevelName?: string;
    /** 规则类型 */
    ruleType?: number;
    /** 规则类型名称 */
    ruleTypeName?: string;
    /** 适用场景 */
    applicableScene?: number;
    /** 适用场景名称 */
    applicableSceneName?: string;
    /** 适用SQL类型 */
    applicableSqlType?: number;
    /** 适用SQL类型名称 */
    applicableSqlTypeName?: string;
    /** 规则目标 */
    ruleTargets?: number;
    /** 规则目标名称 */
    ruleTargetsName?: string;
    /** 规则脚本 */
    ruleScript?: string;
    /** 规则来源 */
    ruleResource?: number;
    /** 规则来源名称 */
    ruleResourceName?: string;
    /** 阈值key */
    thresholdParamKey?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 审核目的 */
    auditPurpose?: number;
    /** 审核目的名称 */
    auditPurposeName?: string;
    /** 审核模式 */
    auditMode?: number;
  };

  type RuleTemplateAddRequest = {
    /** 模板名称 */
    templateName: string;
    /** 模板说明 */
    description: string;
    /** 支持的数据库类型 (可选值: 1-MySQL, 2-Oracle, 3-TDSQL, 4-DM, 5-OB(MySql兼容), 6-OB(Oracle兼容), , 301-系统安全等...) */
    dbType: number;
    auditMode: number;
    /** 规则ID列表 */
    ruleIds: string[];
  };

  type RuleTemplateResponse = {
    /** 规则模板ID */
    id?: string;
    /** 模板名称 */
    templateName?: string;
    /** 模板说明 */
    description?: string;
    /** 数据库类型 */
    dbType?: number;
    /** 数据库类型 */
    dbTypeName?: string;
    /** 是否为系统模板 (1: 系统模板, 2: 用户模板) */
    isSystemTemplate?: number;
    /** 是否为系统模板名称 */
    isSystemTemplateName?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 规则id列表 */
    ruleIds?: string[];
    /** 审核类型 */
    auditMode?: number;
    /** 审核类型名称 */
    auditModeName?: string;
  };

  type RuleTemplateUpdateRequest = {
    /** 规则模板ID */
    id: string;
    /** 模板名称 */
    templateName: string;
    /** 模板说明 */
    description: string;
    /** 规则ID列表 */
    ruleIds: string[];
  };

  type RuleUpdateRequest = {
    /** 规则ID */
    id: string;
    /** 规则名称 */
    ruleName: string;
    /** 规则说明 */
    description?: string;
    /** 规则来源 (可选值: 1-系统规则（不可选）, 2-自定义-shell-脚本, 3-自定义-python-脚本, 4-自定义-代码生成) */
    ruleResource: number;
    /** 告警等级 (可选值: 1-正常, 2-警告, 3-错误) */
    alertLevel: number;
    /** 规则类型 (可选值: 1-DDL规范, 2-索引规范, 3-命令规范, 4-索引优化, 5-全局配置, 6-索引失效, 7-使用建议, 8-DML规范, 9-其它, 301-操作系统安全, 302-数据库安全, 303-中间件安全) */
    ruleType: number;
    /** 适用场景 (可选值: 1-SQL上线|创建工单, 2-自动任务, 3-SQL上线和自动任务) */
    applicableScene: number;
    /** 适用SQL类型 (可选值: 1-Select语句, 2-插入语句, 3-更新语句, 4-删除语句, 34-更新和删除, 123-查询、插入、更新, 5-数据定义语言（DDL）) */
    applicableSqlType: number;
    /** 规则目标 (可选值: 1-存在_字段, 2-命名_字段, 3-禁止_值, 99-无) */
    ruleTargets: number;
    /** 规则脚本 */
    ruleScript: string;
    /** 审核目的类型 (可选值: 1-正确性, 2-可维护性, 3-性能问题, 4-安全性) */
    auditPurpose: number;
    /** 审核模式 (可选值: 1-动态, 2-静态, 3-基线) */
    auditMode: number;
  };

  type SqlAuditRequest = {
    /** SQL语句列表 */
    sql: string[];
    /** 数据源id */
    sourceId: number;
    /** 数据源名称 或 schema 名称 */
    dbNameOrSchema: string;
  };

  type SqlAuditResponse = {
    id?: string;
    /** 数据源的ID */
    sourceId?: number;
    /** 数据源名称 */
    sourceName?: string;
    /** 执行的SQL语句 */
    sqlStatement?: string;
    /** 审核状态 */
    auditStatus?: number;
    /** 审核信息 */
    auditInfo?: string;
    /** sql种类 */
    sqlKind?: string;
    createdAt?: string;
    updatedAt?: string;
    /** 优化建议 */
    optimizedSql?: string;
  };

  type SqlAuditUserResponse = {
    /** 用户Id */
    id?: string;
    /** 用户名 */
    username?: string;
  };

  type SqlFormat = {
    sql?: string;
  };

  type SqlFormatDTO = {
    /** SQL语句 */
    sql: string;
  };

  type SubsysPermissionResponse = {
    /** 权限id */
    id?: string;
    /** 权限名称 */
    name?: string;
    /** 权限类型 */
    type?: number;
    /** 权限类型名称 */
    typeName?: string;
    /** 权限角色描述 */
    description?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
  };

  type SummaryStatisticsResponse = {
    /** 审核类型 */
    auditMode?: number;
    /** 对象类型 */
    dbType?: number;
    /** 对象数量 */
    sourceCount?: number;
    /** 工单数量 */
    workOrderCount?: number;
    /** 规则数量 */
    ruleCount?: number;
    /** 基线要求数量 */
    baselineCount?: number;
    /** 审核次数 */
    auditTime?: number;
    /** 任务次数 */
    taskTime?: number;
  };

  type SystemNotificationVO = {
    id?: string;
    title?: string;
    messageContent?: string;
    redirectInfo?: RedirectInfoVO;
    isRead?: boolean;
    createTime?: string;
  };

  type TaskAddRequest = {
    /** 任务名称 */
    taskName: string;
    /** 任务描述 */
    taskDescription: string;
    /** 执行描述 */
    triggerDescription: string;
    /** 首次执行时间 */
    firsttTime: string;
    /** 执行间隔（单位：小时） */
    interval: number;
    /** 目标数据源ID） */
    sourceId: number;
    /** 数据库实例名、服务名、schema名 */
    dbNameOrSchema?: string;
    /** 存放日志的机器信息 */
    machineInfos?: MachineInfoDTO[];
    /** 审核模式 */
    auditMode: number;
  };

  type TaskResponse = {
    /** 任务名称 */
    taskName: string;
    /** 任务描述 */
    taskDescription: string;
    /** 执行描述 */
    triggerDescription: string;
    /** 首次执行时间 */
    firsttTime: string;
    /** 执行间隔（单位：小时） */
    interval: number;
    /** 目标数据源ID） */
    sourceId: number;
    /** 目标数据源数据库类型） */
    sourceDbType: number;
    /** 目标数据源数据库类型名称） */
    sourceDbTypeName: string;
    /** 目标数据源资源组ID） */
    sourceGroupId?: number;
    /** 目标数据源资源组ID） */
    sourceGroupName?: string;
    /** 数据库实例名、服务名、schema名 */
    dbNameOrSchema: string;
    /** 目标数据源名称 */
    sourceName: string;
    /** 下一次执行时间 */
    nextExecutionTime: string;
    /** 任务状态 */
    taskStatus: string;
    /** 执行次数 */
    executionCount: number;
    /** 机器信息’） */
    machineInfos?: MachineInfo[];
    /** 信息’） */
    message?: string;
    /** 触发状态’） */
    triggerStatus?: string;
    /** 审核模式 */
    auditMode?: number;
    /** 审核模式名称） */
    auditModeName?: string;
  };

  type TaskResultResponse = {
    /** 结果id */
    id?: string;
    /** 任务名称(quartz的id) */
    taskName?: string;
    /** 执行任务的时间 */
    executionTime?: string;
    /** 任务结果状态：1.进行中 2.正常结束 3.异常结束 */
    taskResultStatus?: number;
    /** 错误信息 */
    errorMessage?: string;
    /** 数据库日志文件路径 */
    sqlFilePath?: string;
    /** 采集到的SQL数量 */
    sqlCount?: number;
    /** 采集的文件开始指针位置 */
    fileStartPosition?: string;
    /** 采集的文件结指针位置 */
    fileEndPosition?: string;
    /** 总结花费时间（秒） */
    totalTime?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 机器IP */
    machineIp?: string;
    /** 数据源ID */
    sourceId?: number;
  };

  type TaskResultSqlResponse = {
    /** 明细id */
    id?: string;
    /** sql语句 */
    sqlStatement?: string;
    /** 任务结果ID */
    taskResultId?: string;
    /** 审核结果ID */
    auditResultId?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 数据源ID */
    sourceId?: number;
    /** 数据源名称 */
    sourceName?: string;
    /** 审核结果状态 */
    auditStatus?: number;
    /** 审核信息 */
    auditInfo?: string;
    /** 审核类型 */
    auditMode?: number;
    /** 优化建议 */
    optimizedSql?: string;
  };

  type testConnectionByIdParams = {
    id: number;
  };

  type UserVO = {
    id?: string;
    username?: string;
    phone?: string;
    email?: string;
    subSysSqlAuditPermissions?: string[];
    subSysRolesIds?: string[];
  };

  type violationsRuleStatisticsDetailParams = {
    dbType?: number;
    auditMode?: number;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
    ruleId: string;
    pageNum?: number;
    pageSize?: number;
  };

  type ViolationsRuleStatisticsDetailResponse = {
    /** 规则ID */
    ruleId?: string;
    /** 规则名称 */
    violatedRuleName?: string;
    /** 审核模式 */
    auditMode?: number;
    /** 错误描述 */
    errorMessage?: string;
  };

  type violationsRuleStatisticsExportParams = {
    dbType?: number;
    auditMode?: number;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
  };

  type violationsRuleStatisticsParams = {
    dbType?: number;
    auditMode?: number;
    groupId?: number;
    sourceId?: number;
    startDate: string;
    endDate: string;
    pageNum?: number;
    pageSize?: number;
  };

  type ViolationsRuleStatisticsResponse = {
    /** 对象组 */
    groupName?: string;
    /** 目标对象ID */
    sourceId?: number;
    /** 目标对象名称 */
    sourceName?: string;
    /** 对象类型 */
    dbType?: number;
    /** 规则ID */
    ruleId?: string;
    /** 规则名称 */
    ruleName?: string;
    /** 规则描述 */
    description?: string;
    /** 审核模式 */
    auditMode?: number;
    /** 违反次数 */
    violationsCount?: number;
  };

  type WorkOrderAddRequest = {
    /** 工单名称 */
    workOrderName: string;
    /** SQL 内容 */
    sqlContentList: string[];
    /** 可执行时间范围 */
    executionTimeRange?: string;
    /** 资源组ID */
    resourceGroupId: number;
    /** 数据源ID */
    dataSourceId: number;
    /** 数据库实例名称 */
    dbInstanceName: string;
  };

  type WorkOrderApprovalRecordReponse = {
    /** 审批记录ID */
    id?: string;
    /** 工单ID */
    workOrderId?: number;
    /** 操作类型 */
    operation?: number;
    /** 操作类型名称（如：提交、通过、拒绝等） */
    operationName?: string;
    /** 本次审批的序号 */
    thisApprovalOrder?: number;
    /** 下一步审批角色Id */
    nextApprovalRoleId?: string;
    /** 操作人Id */
    operatorId?: string;
    /** 操作人Name */
    operatorName?: string;
    /** 操作时间 */
    operationTime?: string;
    /** 操作信息（如审批意见或拒绝理由等） */
    operationInfo?: string;
  };

  type WorkOrderApprovalRequest = {
    /** 工单ID */
    workOrderId: number;
    /** 可执行时间范围 */
    executionTimeRange?: string;
    /** 操作 operatorId */
    operation: number;
    /** 审批意见 */
    operationInfo?: string;
  };

  type WorkOrderDetailResponse = {
    /** 工单ID */
    id?: number;
    /** 工单名称 */
    workOrderName?: string;
    /** 可执行时间范围（如：2025-04-01 09:17 -> 2025-04-04 09:17） */
    executionTimeRange?: string;
    /** 资源组ID */
    resourceGroupId?: number;
    /** 资源组名称 */
    resourceGroupName?: string;
    /** 数据源ID */
    dataSourceId?: number;
    /** 数据源名称 */
    dataSourceName?: string;
    /** 数据库实例名称（或 schema 名称） */
    dbInstanceName?: string;
    /**  提交工单的用户ID */
    userId?: string;
    /** 提交工单的用户名称 */
    userName?: string;
    /** SQL种类 */
    sqlKind?: string;
    /** 工单状态 */
    status?: number;
    /** 工单状态 */
    statusName?: string;
    createdAt?: string;
    updatedAt?: string;
    /** 执行用时（秒） */
    executionTime?: number;
    /** 执行用户Id */
    executorUserId?: string;
    /** 执行用户名称 */
    executorUserName?: string;
    /** 工单中的sql详情 */
    workOrderSqlDetail?: WorkOrderSqlDetail[];
  };

  type WorkOrderExecuteRequest = {
    /** 工单ID */
    id: number;
  };

  type WorkOrderResponse = {
    /** 工单ID */
    id?: number;
    /** 工单名称 */
    workOrderName?: string;
    /** 可执行时间范围（如：2025-04-01 09:17 -> 2025-04-04 09:17） */
    executionTimeRange?: string;
    /** 资源组ID */
    resourceGroupId?: number;
    /** 资源组名称 */
    resourceGroupName?: string;
    /** 数据源ID */
    dataSourceId?: number;
    /** 数据源名称 */
    dataSourceName?: string;
    /** 数据库实例名称（或 schema 名称） */
    dbInstanceName?: string;
    /**  提交工单的用户ID */
    userId?: string;
    /** 提交工单的用户名称 */
    userName?: string;
    /** 工单状态 */
    status?: number;
    /** 工单状态名称 */
    statusName?: string;
    createdAt?: string;
    updatedAt?: string;
    /** 执行用时（秒） */
    executionTime?: number;
    /** 执行用户Id */
    executorUserId?: string;
    /** 执行用户名称 */
    executorUserName?: string;
  };

  type WorkOrderSqlDetail = {
    sql?: string;
    auditResultId?: string;
    executeResult?: string;
    kind?: string;
    affectedRows?: string;
  };

  type WorkOrderTerminateRequest = {
    /** 工单ID */
    id: number;
  };
}
