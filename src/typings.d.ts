declare module 'slash2';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare module 'omit.js';
declare module 'numeral';
declare module '@antv/data-set';
declare module 'mockjs';
declare module 'react-fittext';
declare module 'bizcharts-plugin-slider';

declare const REACT_APP_ENV: 'test' | 'dev' | 'pre' | false;

interface Window {
  COMMIT_HASH?: string;
  APP_MODE?: string;
}

//是否支持动态审核
declare const __ENABLE_DYNAMIC_AUDIT_ENABLED__: boolean;
declare const __ENABLE_STATIC_AUDIT_ENABLED__: boolean;
declare const __ENABLE_BASELINE_AUDIT_ENABLED__: boolean;
//类型声明
declare const __FEATURE_TOGGLE__: {
  navBarTitle: string;
  supportedAuditType: number[];
  defaultObjectTypeByAudit: Record<number, number>;
};
