/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: { currentUser?: API.UserVO } | undefined) {
  const { currentUser } = initialState ?? {};
  if (!currentUser) return {};

  const { subSysSqlAuditPermissions: permissions = [] } = currentUser;

  return {
    //菜单权限
    canReadBasicConfig: permissions.includes('MENU_BASIC_CONFIG'), //菜单-基础配置
    canReadApprovalParams: permissions.includes('MENU_APPROVAL_PARAMS'), //菜单-基础配置-审核参数
    canReadMenuRuleTemplate: permissions.includes('MENU_RULE_TEMPLATE'), //菜单-基础配置-审核策略
    canReadRuleManagement: permissions.includes('MENU_RULE_MANAGEMENT'), //菜单-基础配置-规则管理
    canReadResourceManagement: permissions.includes('MENU_RESOURCE_MANAGEMENT'), //菜单-审核对象
    canReadResourceGroup: permissions.includes('MENU_RESOURCE_GROUP'), //菜单-业务资源-对象组
    canReadDataSource: permissions.includes('MENU_DATA_SOURCE'), //菜单-业务资源-数据库
    canReadSQLReview: permissions.includes('MENU_SQL_REVIEW'), //菜单-人工审核
    canReadSQLOnline: permissions.includes('MENU_SQL_ONLINE'), //菜单-人工审核-SQL提交
    canReadSystemSettings: permissions.includes('MENU_SYSTEM_SETTINGS'), //菜单-系统设置
    canReadRoleManagement: permissions.includes('MENU_SYSTEM_SETTINGS_ROLE_MANAGEMENT'), //菜单-系统设置-角色管理
    canReadStatisticsReport: permissions.includes('MENU_STATISTICS_REPORT'), //菜单-统计报表
    canReadStatisticsSummary: permissions.includes('MENU_STATISTICS_SUMMARY_STATISTICS'), //菜单-统计报表-汇总统计
    canReadStatisticsDetails: permissions.includes('MENU_STATISTICS_DETAILED_STATISTICS'), //菜单-统计报表-明细统计
    canReadStatisticsViolationsRule: permissions.includes(
      'MENU_STATISTICS_VIOLATIONS_RULE_STATISTICS',
    ), //菜单-统计报表-违反规则统计
    canReadStatistics: permissions.includes('MENU_STATISTICS_ONLY_REPORT'), //菜单-统计报表-报告
    canReadSQLWorkOrderManagement: permissions.includes('MENU_SQL_WORKORDER_MANAGEMENT'), //菜单-人工审核-工单管理
    canReadAutomaticReview: permissions.includes('MENU_AUTOMATIC_REVIEW'), //菜单-自动审核
    canReadTaskManagement: permissions.includes('MENU_AUTOMATIC_REVIEW_TASK_MANAGEMENT'), //菜单-自动审核-任务管理
    canReadMenuAuditCenter: permissions.includes('MENU_AUDIT_CENTER'), //菜单-审计中心
    canReadMenuAuditCenterSqlRecord: permissions.includes('MENU_AUDIT_CENTER_SQL_RECORD'), //菜单-审计中心-SQL记录
    canReadChannel: permissions.includes('MENU_QUICK_CHANNEL'), //菜单-快速通道
    canReadChannelQuickAudit: permissions.includes('MENU_QUICK_CHANNEL_QUICK_AUDIT'), //菜单-快速通道-快速审核
    //按钮权限
    canSubmitWorkOrder: permissions.includes('BUTTON_SUBMIT_WORKORDER'), //按钮-提交工单
    canApproveWorkOrder: permissions.includes('BUTTON_APPROVE_WORKORDER'), //按钮-审批工单
    canExecuteWorkOrder: permissions.includes('BUTTON_EXECUTE_WORKORDER'), //按钮-执行工单
    canAddTemplate: permissions.includes('BUTTON_ADD_TEMPLATE'), //按钮-增加策略
    canUpdateTemplate: permissions.includes('BUTTON_UPDATE_TEMPLATE'), //按钮-更新策略
    canDeleteTemplate: permissions.includes('BUTTON_DELETE_TEMPLATE'), //按钮-删除策略
    canAddRule: permissions.includes('BUTTON_ADD_RULE'), //按钮-增加规则
    canUpdateRule: permissions.includes('BUTTON_UPDATE_RULE'), //按钮-更新规则
    canDeleteRule: permissions.includes('BUTTON_DELETE_RULE'), //按钮-删除规则
    canAddResourceGroup: permissions.includes('BUTTON_ADD_RESOURCE_GROUP'), //按钮-增加对象组
    canUpdateResourceGroup: permissions.includes('BUTTON_UPDATE_RESOURCE_GROUP'), //按钮-更新对象组
    canDeleteResourceGroup: permissions.includes('BUTTON_DELETE_RESOURCE_GROUP'), //按钮-删除对象组
    canAddDataSource: permissions.includes('BUTTON_ADD_DATA_SOURCE'), //按钮-增加数据库
    canUpdateDataSource: permissions.includes('BUTTON_UPDATE_DATA_SOURCE'), //按钮-更新数据库
    canDeleteDataSource: permissions.includes('BUTTON_DELETE_DATA_SOURCE'), //按钮-删除数据库
    canSqlApprove: permissions.includes('BUTTON_SQL_APPROVE'), //按钮-人工审核
    canCreateTask: permissions.includes('BUTTON_CREATE_TASK'), //按钮-创建任务
    canDeleteTask: permissions.includes('BUTTON_DELETE_TASK'), //按钮-删除任务
    canAssignUserToResourceGroup: permissions.includes('BUTTON_ASSIGN_USER_TO_RESOURCE_GROUP'), //按钮-给资源组分配用户
    canAddRole: permissions.includes('BUTTON_ADD_ROLE'), //按钮-添加角色
    canDeleteRole: permissions.includes('BUTTON_DELETE_ROLE'), //按钮-删除角色
    canUpdateRole: permissions.includes('BUTTON_UPDATE_ROLE'), //按钮-更新角色
    canAssignPermissionToRole: permissions.includes('BUTTON_ASSIGN_PERMISSION_TO_ROLE'), //按钮-给角色分配权限
    canAssignUserToRole: permissions.includes('BUTTON_ASSIGN_USER_TO_ROLE'), //按钮-给角色分配用户
    canSetApprovalFlow: permissions.includes('BUTTON_SET_APPROVAL_FLOW'), //按钮-设置审批流程
    //后台权限，分配权限时使用
    canSubmitWorkOrderOp: permissions.includes('EXECUTE_SUBMIT_WORK_ORDER'), //操作-提交工单
    canApproveWorkOrderOp: permissions.includes('EXECUTE_APPROVE_WORK_ORDER'), //操作-审批工单
    canExecuteWorkOrderOp: permissions.includes('EXECUTE_EXECUTE_WORK_ORDER'), //操作-执行工单
  };
}
