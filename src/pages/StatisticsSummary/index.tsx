import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import { summaryStatistics } from '@/services/sql-guard/statistics';
import { option2enum } from '@/utils';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { request, useRequest } from '@umijs/max';
import { Button, Tag, message } from 'antd';
import React from 'react';

const columns: ProColumns<API.SummaryStatisticsResponse>[] = [
  {
    title: '对象类型',
    dataIndex: 'dbType',
    render: (value) => {
      const typeItem = DATASOURCE_TYPE.find((item) => Number(item.value) === value);
      return <Tag color={typeItem?.color}>{typeItem?.label || value}</Tag>;
    },
  },
  {
    title: '审核类型',
    dataIndex: 'auditMode',
    width: 100,
    valueEnum: option2enum(RULE_AUDIT_MODE),
  },
  {
    title: '对象数量',
    dataIndex: 'sourceCount',
  },
  ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
    ? [
        {
          title: '工单数量',
          dataIndex: 'workOrderCount',
        },
      ]
    : []),
  {
    title: '规则数量',
    dataIndex: 'ruleCount',
  },
  ...(__ENABLE_BASELINE_AUDIT_ENABLED__
    ? [
        {
          title: '基线要求数量',
          dataIndex: 'baselineCount',
        },
      ]
    : []),
  {
    title: '审核次数',
    dataIndex: 'auditTime',
  },
  {
    title: '任务次数',
    dataIndex: 'taskTime',
  },
];

const StatisticsSummary: React.FC = () => {
  const { data, loading } = useRequest(() => summaryStatistics(), {
    formatResult: (res) => res.data || [],
  });

  const handleExport = async () => {
    request('/api/v1/sqlaudit/statistics/summaryStatistics/export', {
      method: 'get',
      responseType: 'blob',
      getResponse: true,
      skipErrorHandler: true,
    })
      .then((response) => {
        const url = URL.createObjectURL(
          new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', `汇总统计`); // 指定下载的文件名和类型
        document.body.appendChild(link);
        link.click();
      })
      .catch(() => {
        message.error('导出失败');
      });
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        columns={columns}
        dataSource={data}
        loading={loading}
        search={false} // 取消查询条件
        pagination={false} // 取消分页
        rowKey="dbType"
        toolBarRender={() => [
          <Button key="export" type="primary" onClick={handleExport}>
            导出
          </Button>,
        ]}
      />
    </PageContainer>
  );
};

export default StatisticsSummary;
