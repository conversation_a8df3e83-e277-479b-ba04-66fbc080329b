import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import { useDataSourceList } from '@/hooks/useDataSourceList';
import { useListAuthGroup } from '@/hooks/useResourceGroupList';
import {
  violationsRuleStatistics,
  violationsRuleStatisticsDetail,
} from '@/services/sql-guard/statistics';
import { option2enum } from '@/utils';
import { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { request } from '@umijs/max';
import { Button, FormInstance, message } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';

import { RenderErrorMessage } from '@/components/RightContent';

// 默认时间范围：前一周，结束日期改为今天
const defaultDateRange = [dayjs().subtract(7, 'day').startOf('day'), dayjs().endOf('day')];

const dateTimeFormat = 'YYYY-MM-DD HH:mm:ss';

const ViolationsRuleStatistics: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [currPageSize, setCurrPageSize] = useState(10);
  const formRef = useRef<FormInstance>();
  const { resourceGroupList, resourceGroupLoading } = useListAuthGroup();
  const [selectedDbType, setSelectedDbType] = useState<number>();
  const { dataSourceList, dataSourceLoading, runDataSource } = useDataSourceList();

  const expandedRowRender = (record: API.ViolationsRuleStatisticsResponse) => {
    const detailColumns: ProColumns<API.ViolationsRuleStatisticsDetailResponse>[] = [
      {
        title: '序号',
        dataIndex: 'index',
        width: 80,
        render: (_, __, index) => index + 1,
      },
      {
        title: '规则名称',
        dataIndex: 'violatedRuleName',
        width: 150,
        ellipsis: true,
      },
      {
        title: '审核模式',
        dataIndex: 'auditMode',
        width: 50,
        valueType: 'select',
        valueEnum: option2enum(RULE_AUDIT_MODE),
        render: (_, record) => {
          const item = RULE_AUDIT_MODE.find((i) => i.value === record.auditMode);
          return item?.label;
        },
      },
      {
        title: '错误描述',
        dataIndex: 'errorMessage',
        width: 300,
        ellipsis: true,
        render: RenderErrorMessage,
      },
    ];

    return (
      <ProTable<API.ViolationsRuleStatisticsDetailResponse>
        {...{
          rowKey: 'id',
          columns: detailColumns,
          pagination: false,
          search: false,
          options: false,
          request: async (params) => {
            const values = formRef.current?.getFieldsValue() || {};
            const { dateRange, ...rest } = values;
            const startDate = dateRange?.[0]
              ? dayjs(dateRange[0]).format(dateTimeFormat)
              : defaultDateRange[0].format(dateTimeFormat);
            const endDate = dateRange?.[1]
              ? dayjs(dateRange[1]).format(dateTimeFormat)
              : defaultDateRange[1].format(dateTimeFormat);

            const msg = await violationsRuleStatisticsDetail({
              ...rest,
              startDate,
              endDate,
              pageNum: params.current,
              pageSize: params.pageSize,
              ruleId: record.ruleId,
            });
            return {
              success: true,
              data: msg.data?.records || [],
              total: Number(msg.data?.total) || 0,
            };
          },
        }}
      />
    );
  };

  const columns: ProColumns<API.ViolationsRuleStatisticsResponse>[] = [
    {
      title: '时间范围',
      dataIndex: 'dateRange',
      valueType: 'dateRange',
      hideInTable: true,
      colSize: 2,
      initialValue: defaultDateRange,
      fieldProps: {
        showTime: { format: dateTimeFormat },
        format: dateTimeFormat,
      },
    },
    {
      title: '序号',
      dataIndex: 'index',
      width: 80,
      hideInSearch: true,
      render: (_, record, index) => {
        return (currentPage - 1) * currPageSize + index + 1;
      },
    },
    {
      title: '对象组',
      dataIndex: 'groupName',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '对象组',
      dataIndex: 'groupId',
      valueType: 'select',
      fieldProps: {
        options: resourceGroupList,
        loading: resourceGroupLoading,
        showSearch: true,
        fieldNames: {
          label: 'groupName',
          value: 'groupId',
        },
      },
      hideInTable: true,
    },
    {
      title: '审核类型',
      dataIndex: 'auditMode',
      width: 100,
      valueType: 'select',
      valueEnum: option2enum(RULE_AUDIT_MODE),
      fieldProps: (form) => ({
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        onChange: () => {
          form?.setFieldValue('dbType', undefined);
          form?.setFieldValue('sourceId', undefined);
          setSelectedDbType(undefined);
        },
      }),
      render: (_, record) => {
        const item = RULE_AUDIT_MODE.find((i) => i.value === record.auditMode);
        return item?.label;
      },
    },
    {
      title: '对象类型',
      width: 130,
      dataIndex: 'dbType',
      valueType: 'select',
      dependencies: ['auditMode'],
      fieldProps: (form) => {
        const auditMode = Number(form?.getFieldValue?.('auditMode'));
        let filteredOptions: typeof DATASOURCE_TYPE = [];

        if (auditMode === 3) {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 301 && itemValue <= 308;
          });
        } else {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 1 && itemValue <= 6;
          });
        }
        return {
          disabled: !auditMode,
          showSearch: true,
          filterOption: true,
          optionFilterProp: 'label',
          options: filteredOptions,
          placeholder: '请选择对象类型',
          onChange: (value: number) => {
            setSelectedDbType(value);
            runDataSource(value, auditMode);
            form?.setFieldValue('sourceId', undefined);
          },
        };
      },
      render: (_, record) => {
        const item = DATASOURCE_TYPE.find((i) => Number(i.value) === record.dbType);
        return item?.label;
      },
    },
    {
      title: '目标对象',
      dataIndex: 'sourceName',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '目标对象',
      dataIndex: 'sourceId',
      width: 150,
      hideInTable: true,
      valueType: 'select',
      dependencies: ['dbType'],
      fieldProps: {
        disabled: !selectedDbType || dataSourceLoading,
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        options: dataSourceList?.map((item) => ({
          label: item.sourceName,
          value: item.sourceId,
        })),
        loading: dataSourceLoading,
        placeholder: '请选择目标对象',
      },
    },
    {
      title: '规则名称',
      width: 200,
      dataIndex: 'ruleName',
      ellipsis: true,
      search: false,
    },
    {
      title: '规则描述',
      width: 200,
      dataIndex: 'description',
      ellipsis: true,
      search: false,
    },
    {
      title: '违反次数',
      width: 100,
      dataIndex: 'violationsCount',
      hideInSearch: true,
    },
  ];

  const handleExport = async () => {
    const values = formRef.current?.getFieldsValue() || {};
    const { dateRange, ...rest } = values;
    const startDate = dateRange?.[0]
      ? dayjs(dateRange[0]).format(dateTimeFormat)
      : defaultDateRange[0].format(dateTimeFormat);
    const endDate = dateRange?.[1]
      ? dayjs(dateRange[1]).format(dateTimeFormat)
      : defaultDateRange[1].format(dateTimeFormat);

    const params = {
      ...rest,
      startDate,
      endDate,
    };

    request('/api/v1/sqlaudit/statistics/violationsRuleStatistics/export', {
      method: 'get',
      params,
      responseType: 'blob',
      getResponse: true,
      skipErrorHandler: true,
    })
      .then((response) => {
        const url = URL.createObjectURL(
          new Blob([response.data], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', `违反规则统计`);
        document.body.appendChild(link);
        link.click();
      })
      .catch(() => {
        message.error('导出失败');
      });
  };

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.ViolationsRuleStatisticsResponse>
        formRef={formRef}
        rowKey="ruleId"
        scroll={{ x: '100%' }}
        columns={columns}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        expandable={{
          expandedRowRender,
        }}
        request={async ({ current: pageNum, pageSize, dateRange, ...params }) => {
          setCurrentPage(pageNum || 1);
          setCurrPageSize(pageSize || 10);
          const startDate = dateRange?.[0]
            ? dayjs(dateRange[0]).format(dateTimeFormat)
            : defaultDateRange[0].format(dateTimeFormat);
          const endDate = dateRange?.[1]
            ? dayjs(dateRange[1]).format(dateTimeFormat)
            : defaultDateRange[1].format(dateTimeFormat);

          const msg = await violationsRuleStatistics({
            ...params,
            startDate,
            endDate,
            pageNum,
            pageSize,
          });
          return {
            success: true,
            data: msg.data?.records || [],
            total: Number(msg.data?.total) || 0,
          };
        }}
        toolBarRender={() => [
          <Button key="export" type="primary" onClick={handleExport}>
            导出
          </Button>,
        ]}
      />
    </PageContainer>
  );
};

export default ViolationsRuleStatistics;
