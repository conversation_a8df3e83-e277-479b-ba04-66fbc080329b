import { DATASOURCE_TYPE, RULE_AUDIT_MODE } from '@/enums';
import { deleteRule, listRules } from '@/services/sql-guard/rule';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { Button, Modal, Space, Tag, message } from 'antd';
import { useRef, useState } from 'react';
import BaseLineDetailModal from './components/BaseLineDetailModal';

const Rules: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const { canAddRule = false, canUpdateRule = false, canDeleteRule = false } = useAccess();
  const [baseLineModalVisible, setBaseLineModalVisible] = useState<boolean>(false);
  const [selectedRuleId, setSelectedRuleId] = useState<string>('');

  //删除
  const { run: deleteRecord } = useRequest((id) => deleteRule(id), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      message.success(res.message);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (row: API.RuleResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${row.ruleName!}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord({ id: Number(row.id) });
      },
    });
  };

  const onEdit = (record: API.RuleResponse) => {
    history.push(`/basic/rules/edit/${record.id}`);
  };

  const showBaseLineDetails = (record: API.RuleResponse) => {
    setSelectedRuleId(record.id || '');
    setBaseLineModalVisible(true);
  };

  const columns: ProColumns<API.RuleResponse>[] = [
    {
      title: '规则名称',
      dataIndex: 'ruleName',
      fixed: 'left',
      width: 250,
      ellipsis: true,
      copyable: true,
      render: (dom, record) => {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/basic/rules/detail/${record.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '审核类型',
      dataIndex: 'auditMode',
      valueType: 'select',
      width: 100,
      ellipsis: true,
      initialValue: String(RULE_AUDIT_MODE[0]?.value),
      valueEnum: option2enum(RULE_AUDIT_MODE),
      fieldProps: (form) => ({
        allowClear: false,
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
        onChange: (val) => {
          form?.setFieldValue(
            'dbType',
            String(__FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[Number(val)]),
          );
        },
      }),
    },
    {
      title: '对象类型',
      dataIndex: 'dbType',
      valueType: 'select',
      width: 170,
      ellipsis: true,
      dependencies: ['auditMode'],
      initialValue: String(
        __FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[RULE_AUDIT_MODE[0]?.value],
      ),
      fieldProps: (form) => {
        const auditMode = Number(form?.getFieldValue?.('auditMode'));
        let filteredOptions: typeof DATASOURCE_TYPE = [];

        if (auditMode === 3) {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 301 && itemValue <= 308;
          });
        } else {
          filteredOptions = DATASOURCE_TYPE.filter((item) => {
            const itemValue = Number(item.value);
            return itemValue >= 1 && itemValue <= 6;
          });
        }

        return {
          allowClear: false,
          showSearch: true,
          filterOption: true,
          optionFilterProp: 'label',
          options: filteredOptions,
          placeholder: '请选择对象类型',
        };
      },
      renderText: (text, record) => {
        const obj = DATASOURCE_TYPE.find((item) => Number(item.value) === record.dbType);
        return <Tag color={obj?.color}>{obj?.label}</Tag>;
      },
    },
    ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
      ? [
          {
            title: '审核目的',
            dataIndex: 'auditPurposeName',
            width: 100,
            ellipsis: true,
            hideInSearch: true,
          },
        ]
      : []),
    {
      title: '告警等级',
      dataIndex: 'alertLevelName',
      width: 80,
      ellipsis: true,
      hideInSearch: true,
    },
    ...(__ENABLE_DYNAMIC_AUDIT_ENABLED__
      ? [
          {
            title: '参数',
            dataIndex: 'thresholdParamKey',
            width: 150,
            ellipsis: true,
            hideInSearch: true,
          },
        ]
      : []),
    {
      title: '操作',
      width: 170,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <>
            <Space>
              {record.auditMode === 3 && (
                <a onClick={() => showBaseLineDetails(record)}>查看基线要求</a>
              )}
              {record.ruleResource !== 1 && (
                <>
                  <Access key="edit" accessible={canUpdateRule}>
                    <a onClick={() => onEdit(record)}>编辑</a>
                  </Access>
                  <Access key="del" accessible={canDeleteRule}>
                    <a onClick={() => handleDelete(record)}>删除</a>
                  </Access>
                </>
              )}
            </Space>
          </>
        );
      },
    },
  ];
  return (
    <PageContainer header={{ title: false }}>
      <ProTable<API.RuleResponse>
        {...defaultTableConfig}
        actionRef={tableRef}
        scroll={{ x: '100%' }}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
        }}
        columns={columns}
        headerTitle="规则列表"
        toolbar={{
          actions: [
            <Access key="add" accessible={canAddRule}>
              {__ENABLE_DYNAMIC_AUDIT_ENABLED__ && (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    history.push('/basic/rules/add');
                  }}
                >
                  新建规则
                </Button>
              )}
            </Access>,
          ],
        }}
        request={async (params) => queryPagingTable<API.listRulesParams>(params, listRules)}
      />
      <BaseLineDetailModal
        open={baseLineModalVisible}
        onCancel={() => setBaseLineModalVisible(false)}
        ruleId={selectedRuleId}
      />
    </PageContainer>
  );
};

export default Rules;
