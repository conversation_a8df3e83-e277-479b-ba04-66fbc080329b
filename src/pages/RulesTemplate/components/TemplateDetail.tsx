import RKCol from '@/components/RKCol';
import {
  DATASOURCE_TYPE,
  RULE_AUDIT_MODE,
  RULE_RESOURCE,
  RULE_TARGETS_TYPE,
  RULE_TYPE,
} from '@/enums';
import WithRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import {
  addRuleTemplate,
  detailRuleTemplate,
  listRuleByDbType,
  listRuleByTemplateId,
  updateRuleTemplate,
} from '@/services/sql-guard/ruleTemplate';
import { onSuccessAndGoBack, option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { history, useLocation, useRequest } from '@umijs/max';
import { Row } from 'antd';
import { useRef, useState } from 'react';
import BaseLineDetailModal from './BaseLineDetailModal';

const TemplateDetail: React.FC<WithRouteEditingProps> = ({ id, isEditPage }) => {
  const formRef = useRef<ProFormInstance>();
  const tableRef = useRef<ActionType>();
  const [selectedRulesIds, setSelectedRulesIds] = useState<string[]>([]);

  const defaultAuditMode = RULE_AUDIT_MODE[0]?.value;
  const defaultDbType = Number(__FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[defaultAuditMode]);

  const [ruleDbType, setRuleDbType] = useState<number>(defaultDbType); //对象类型
  const [auditMode, setAuditMode] = useState<number>(defaultAuditMode); //审核模式

  const [baseLineModalVisible, setBaseLineModalVisible] = useState<boolean>(false);
  const [selectedRuleId, setSelectedRuleId] = useState<string>('');
  const { pathname } = useLocation();
  const isDetailPage = pathname.includes('detail');
  const isAddPage = pathname.includes('add');

  const { run: add, loading: addLoading } = useRequest((value) => addRuleTemplate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  const { run: edit, loading: editLoading } = useRequest((value) => updateRuleTemplate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  const { data = {}, loading: detailLoading } = useRequest(() => detailRuleTemplate({ id }), {
    ready: !!id,
    onSuccess: (res) => {
      if (res.code !== 200) return;
      const { ruleIds, auditMode } = res.data as API.RuleTemplateResponse;
      setSelectedRulesIds(ruleIds!);
      setAuditMode(auditMode as number);
      formRef.current?.setFieldsValue({ ...res.data });
    },
    formatResult: (res) => res,
  });

  const columns: ProColumns<API.RuleResponse>[] = [
    {
      title: '规则名称',
      dataIndex: 'ruleName',
      fixed: 'left',
      width: 250,
      ellipsis: true,
      copyable: true,
      render: (dom, record) => {
        return (
          <a className="rk-a-span" onClick={() => history.push(`/basic/rules/detail/${record.id}`)}>
            {dom}
          </a>
        );
      },
    },
    {
      title: '规则类型',
      dataIndex: 'ruleType',
      width: 120,
      ellipsis: true,
      valueType: 'select',
      valueEnum: isAddPage
        ? option2enum(
            RULE_TYPE.filter((item) => {
              const itemValue = Number(item.value);
              if (auditMode === 3) {
                return [301, 302, 303].includes(itemValue);
              }
              return [1, 2, 3, 4, 5, 6, 7, 8, 9, 99].includes(itemValue);
            }),
          )
        : option2enum(RULE_TYPE),
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
      },
    },
    {
      title: '规则来源',
      dataIndex: 'ruleResource',
      width: 120,
      ellipsis: true,
      valueType: 'select',
      valueEnum: option2enum(RULE_RESOURCE),
      fieldProps: {
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
      },
      hideInTable: true,
    },
    {
      title: '规则目标',
      dataIndex: 'ruleTargets',
      width: 100,
      ellipsis: true,
      valueType: 'select',
      valueEnum: option2enum(RULE_TARGETS_TYPE),
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
      },
    },
    {
      title: '审核类型',
      dataIndex: 'auditMode',
      valueType: 'select',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
      valueEnum: option2enum(RULE_AUDIT_MODE),
      fieldProps: {
        showSearch: true,
        filterOption: true,
        optionFilterProp: 'label',
      },
    },
    {
      title: '规则类型',
      dataIndex: 'ruleTypeName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '规则来源',
      dataIndex: 'ruleResourceName',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '规则目标',
      dataIndex: 'ruleTargetsName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '告警等级',
      dataIndex: 'alertLevelName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },

    {
      title: '适用场景',
      dataIndex: 'applicableSceneName',
      width: 100,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '适用SQL类型',
      dataIndex: 'applicableSqlTypeName',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '规则说明',
      dataIndex: 'description',
      width: 300,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        if (record.auditMode === 3) {
          return (
            <a
              onClick={() => {
                setSelectedRuleId(record.id!);
                setBaseLineModalVisible(true);
              }}
            >
              查看基线要求
            </a>
          );
        }
        return null;
      },
    },
  ];

  return (
    <PageContainer header={{ title: false }} className="detail-container" loading={detailLoading}>
      <ProForm
        formRef={formRef}
        disabled={isDetailPage}
        initialValues={data.data}
        submitter={
          isDetailPage
            ? false
            : {
                searchConfig: {
                  submitText: '保存',
                  resetText: '取消',
                },
                onReset: () => {
                  history.go(-1);
                },
                render: (props, doms) => {
                  return (
                    <FooterToolbar
                      extra={
                        selectedRulesIds.length > 0 && (
                          <div>
                            已选择{' '}
                            <a
                              style={{
                                fontWeight: 600,
                              }}
                            >
                              {selectedRulesIds.length}
                            </a>{' '}
                            项
                          </div>
                        )
                      }
                    >
                      {doms}
                    </FooterToolbar>
                  );
                },
                submitButtonProps: {
                  loading: addLoading || editLoading,
                },
              }
        }
        onFinish={async (value) => {
          const formData = {
            ...value,
            ruleIds: selectedRulesIds,
          };
          const msg = isEditPage
            ? await edit({ ...formData, id: Number(id) })
            : await add(formData);
          const success = msg.code === 200;
          return success;
        }}
      >
        <div className="rk-none">
          <ProFormText name="ruleIds" label="ruleIds" placeholder="请输入" />
          <ProFormText name="id" label="id" placeholder="请输入" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText
              name="templateName"
              label="策略名称"
              placeholder="请输入"
              rules={[requiredRule]}
              disabled={isEditPage}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="auditMode"
              initialValue={RULE_AUDIT_MODE[0]?.value}
              label="审核类型"
              options={RULE_AUDIT_MODE}
              fieldProps={{
                showSearch: true,
                filterOption: true,
                optionFilterProp: 'label',
                onChange: (val) => {
                  if (val) {
                    setAuditMode(val as number);
                    formRef?.current?.setFieldValue('dbType', String(defaultDbType));
                    setRuleDbType(Number(defaultDbType));
                  }
                },
              }}
              rules={[requiredRule]}
              disabled={isEditPage}
              placeholder="请选择"
            />
          </RKCol>
          <ProFormDependency name={['auditMode']}>
            {({ auditMode }) => {
              let filteredOptions: typeof DATASOURCE_TYPE = [];
              if (auditMode === 3) {
                filteredOptions = DATASOURCE_TYPE.filter((item) => {
                  const itemValue = Number(item.value);
                  return itemValue >= 301 && itemValue <= 308;
                });
              } else {
                filteredOptions = DATASOURCE_TYPE.filter((item) => {
                  const itemValue = Number(item.value);
                  return itemValue >= 1 && itemValue <= 6;
                });
              }
              return (
                <RKCol>
                  <ProFormSelect
                    name="dbType"
                    label="对象类型"
                    placeholder="请选择"
                    options={filteredOptions}
                    disabled={isEditPage}
                    initialValue={String(
                      __FEATURE_TOGGLE__.defaultObjectTypeByAudit?.[RULE_AUDIT_MODE[0]?.value],
                    )}
                    fieldProps={{
                      showSearch: true,
                      filterOption: true,
                      optionFilterProp: 'label',
                      onChange: (value?: string) => {
                        if (value) {
                          setRuleDbType(Number(value));
                        } else {
                          setRuleDbType(0);
                        }
                      },
                    }}
                    transform={(value: string, namePath: string) => {
                      return { [namePath]: value && Number(value) };
                    }}
                    convertValue={(value) => value?.toString()}
                    rules={[requiredRule]}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormTextArea
              name="description"
              label="策略说明"
              placeholder="请输入"
              fieldProps={{ autoSize: { minRows: 1, maxRows: 3 } }}
              rules={[requiredRule]}
            />
          </RKCol>
        </Row>
      </ProForm>
      <ProTable<API.RuleResponse>
        {...defaultTableConfig}
        // scroll={{ x: '100%' }}
        actionRef={tableRef}
        params={isDetailPage ? { templateId: Number(id) } : { dbType: ruleDbType, auditMode }}
        style={{ marginTop: 20 }}
        search={{
          defaultCollapsed: false,
          filterType: 'query',
          //屏蔽收起按钮
          collapseRender: () => null,
        }}
        rowSelection={
          ruleDbType !== 0 && !isDetailPage
            ? {
                selectedRowKeys: selectedRulesIds,
                onSelect: (record, selected) => {
                  if (selected === false) {
                    const newSelectedIds = selectedRulesIds.filter((it) => it !== record.id);
                    setSelectedRulesIds(newSelectedIds);
                  } else {
                    setSelectedRulesIds(Array.from(new Set([...selectedRulesIds, record.id!])));
                  }
                },
                onSelectAll: (selected, selectedRow, changeRows) => {
                  const changeIds = changeRows.map((i) => i.id!);
                  if (selected === false) {
                    const newSelectedRows = selectedRulesIds.filter(
                      (it) => !changeIds.includes(it),
                    );
                    setSelectedRulesIds(newSelectedRows);
                  } else {
                    setSelectedRulesIds(Array.from(new Set([...changeIds, ...selectedRulesIds])));
                  }
                },
              }
            : false
        }
        columns={columns}
        headerTitle="规则列表"
        request={async (params) => {
          const msg = await queryPagingTable<
            API.listRuleByDbTypeParams & API.listRuleByTemplateIdParams
          >({ ...params }, isDetailPage ? listRuleByTemplateId : listRuleByDbType);

          const { total, records } = msg;

          return {
            data: records || [],
            success: true,
            total: Number(total) || 0,
          };
        }}
      />
      <BaseLineDetailModal
        open={baseLineModalVisible}
        onCancel={() => setBaseLineModalVisible(false)}
        ruleId={selectedRuleId}
        templateId={Number(id)}
        isAddPage={isAddPage}
        isDetailPage={isDetailPage}
      />
    </PageContainer>
  );
};

export default WithRouteEditing(TemplateDetail);
